<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/MultiRppExam.php';
require_once '../models/Guru.php';

// Cek jika bukan POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Akses tidak valid.";
    header("Location: multi_rpp_generate.php");
    exit();
}

// Validasi input
if (!isset($_POST['selected_rpps']) || !isset($_POST['exam_title'])) {
    $_SESSION['error'] = "Data tidak lengkap.";
    header("Location: multi_rpp_generate.php");
    exit();
}

// Ambil data dari form
$selected_rpp_ids = $_POST['selected_rpps'];
$exam_data = [
    'exam_title' => $_POST['exam_title'],
    'exam_type' => $_POST['exam_type'],
    'semester' => $_POST['semester'],
    'tahun_ajaran' => $_POST['tahun_ajaran'],
    'exam_duration' => $_POST['exam_duration'],
    'total_score' => $_POST['total_score'],
    'additional_notes' => $_POST['additional_notes'] ?? ''
];

$config = [
    'multiple_choice_count' => (int)$_POST['multiple_choice_count'],
    'essay_count' => (int)$_POST['essay_count'],
    'multiple_choice_options' => (int)$_POST['multiple_choice_options'],
    'regular_count' => (int)$_POST['regular_count'],
    'hots_easy_count' => (int)$_POST['hots_easy_count'],
    'hots_medium_count' => (int)$_POST['hots_medium_count'],
    'hots_hard_count' => (int)$_POST['hots_hard_count'],
    'distribution_strategy' => $_POST['distribution_strategy'] ?? 'proportional'
];

// Validasi total soal
$total_by_type = $config['multiple_choice_count'] + $config['essay_count'];
$total_by_difficulty = $config['regular_count'] + $config['hots_easy_count'] + 
                      $config['hots_medium_count'] + $config['hots_hard_count'];
$max_total = max($total_by_type, $total_by_difficulty);

if ($max_total > 10 || $max_total === 0) {
    $_SESSION['error'] = "Total soal harus antara 1-10.";
    header("Location: multi_rpp_configure.php");
    exit();
}

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data RPP yang dipilih
$rpp = new Rpp();
$selected_rpps = [];
foreach ($selected_rpp_ids as $rpp_id) {
    $stmt = $rpp->getOne($rpp_id);
    $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
        $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
        header("Location: multi_rpp_generate.php");
        exit();
    }
    
    $selected_rpps[] = $rpp_data;
}

// Store configuration for AJAX processing
$_SESSION['multi_rpp_config'] = $config;
$_SESSION['multi_rpp_exam_data'] = $exam_data;
$_SESSION['multi_rpp_selected_rpps'] = $selected_rpp_ids;
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Generate Soal Multi-RPP AI</h5>
            <a href="multi_rpp_configure.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali ke Konfigurasi
            </a>
        </div>
        <div class="card-body">
            <!-- Multi-RPP Info -->
            <div class="alert alert-info">
                <strong>Multi-RPP Exam:</strong> 
                <?php 
                $rpp_names = array_map(function($rpp) { 
                    return $rpp['tema_subtema']; 
                }, $selected_rpps);
                echo implode(' • ', $rpp_names);
                ?>
                (<?= count($selected_rpps) ?> RPP)
            </div>

            <!-- Configuration Summary -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Konfigurasi Soal</h6>
                            <ul class="list-unstyled mb-0">
                                <li><strong>Pilihan Ganda:</strong> <?= $config['multiple_choice_count'] ?> soal</li>
                                <li><strong>Essay:</strong> <?= $config['essay_count'] ?> soal</li>
                                <li><strong>Opsi Jawaban:</strong> <?= $config['multiple_choice_options'] ?> pilihan</li>
                                <li><strong>Total Soal:</strong> <?= $max_total ?> soal</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Distribusi Kesulitan</h6>
                            <ul class="list-unstyled mb-0">
                                <li><strong>Regular:</strong> <?= $config['regular_count'] ?> soal</li>
                                <li><strong>HOTS Mudah:</strong> <?= $config['hots_easy_count'] ?> soal</li>
                                <li><strong>HOTS Sedang:</strong> <?= $config['hots_medium_count'] ?> soal</li>
                                <li><strong>HOTS Tinggi:</strong> <?= $config['hots_hard_count'] ?> soal</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Initial State -->
            <div id="initial-state">
                <div class="text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-magic fa-3x text-primary"></i>
                    </div>
                    <h5>Siap Generate Soal Multi-RPP</h5>
                    <p class="text-muted">Klik tombol di bawah untuk memulai proses generate soal dengan AI</p>
                    <button type="button" class="btn btn-primary btn-lg" onclick="startGeneration()">
                        <i class="fas fa-play"></i> Mulai Generate Soal
                    </button>
                </div>
            </div>

            <!-- Loading State -->
            <div id="loading-state" class="d-none">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5>Sedang Generate Soal...</h5>
                    <p class="text-muted mb-3" id="progress-text">Memulai proses generate...</p>
                    
                    <div class="progress mb-3" style="height: 20px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="progress-bar">
                        </div>
                    </div>
                    
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Proses ini dapat memakan waktu 30-60 detik tergantung kompleksitas soal
                    </small>
                </div>
            </div>

            <!-- Error State -->
            <div id="error-state" class="d-none">
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Gagal Generate Soal</h6>
                    <div id="error-message"></div>
                </div>
                <div class="text-center">
                    <button type="button" class="btn btn-primary me-2" onclick="startGeneration()">
                        <i class="fas fa-redo"></i> Coba Lagi
                    </button>
                    <a href="multi_rpp_configure.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali ke Konfigurasi
                    </a>
                </div>
            </div>

            <!-- Success State -->
            <div id="success-state" class="d-none">
                <form action="multi_rpp_save.php" method="POST" id="saveForm">
                    <!-- Hidden fields will be populated by JavaScript -->
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6>Pilih soal yang akan disimpan: (<span id="total-questions">0</span> soal)</h6>
                        <div>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                                <i class="fas fa-check-square"></i> Pilih Semua
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="deselectAll()">
                                <i class="fas fa-square"></i> Batal Pilih
                            </button>
                        </div>
                    </div>

                    <div id="questions-container">
                        <!-- Questions will be loaded here by JavaScript -->
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save"></i> Simpan Soal Terpilih
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.question-text {
    font-size: 1.1rem;
    line-height: 1.6;
}

.option-item {
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #f8f9fa;
    border-left: 3px solid #dee2e6;
}

.option-item.correct-answer {
    background-color: #d4edda;
    border-left-color: #28a745;
    font-weight: 500;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.card-header .form-check {
    margin-bottom: 0;
}

.badge {
    font-size: 0.8rem;
}

.progress {
    background-color: #e9ecef;
}

.progress-bar {
    background-color: #007bff;
}

#error-state .alert {
    margin-bottom: 1rem;
}
</style>

<script>
let progressInterval;
let generationTimeout;

function startGeneration() {
    // Hide all states
    document.getElementById('initial-state').classList.add('d-none');
    document.getElementById('error-state').classList.add('d-none');
    document.getElementById('success-state').classList.add('d-none');

    // Show loading state
    document.getElementById('loading-state').classList.remove('d-none');

    // Start progress simulation
    simulateProgress();

    // Set timeout for generation (5 minutes)
    generationTimeout = setTimeout(() => {
        clearInterval(progressInterval);
        showErrorState('Timeout: Proses generate memakan waktu terlalu lama. Silakan coba lagi.', 'timeout');
    }, 300000);

    // Start AJAX request
    const formData = new FormData();

    // Add all session data
    <?php foreach ($selected_rpp_ids as $rpp_id): ?>
    formData.append('selected_rpps[]', '<?= $rpp_id ?>');
    <?php endforeach; ?>

    <?php foreach ($exam_data as $key => $value): ?>
    formData.append('exam_data[<?= $key ?>]', '<?= htmlspecialchars($value) ?>');
    <?php endforeach; ?>

    <?php foreach ($config as $key => $value): ?>
    formData.append('config[<?= $key ?>]', '<?= htmlspecialchars($value) ?>');
    <?php endforeach; ?>

    fetch('ajax_generate_multi_rpp_questions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);
        clearTimeout(generationTimeout);

        if (data.success) {
            // Complete progress bar
            updateProgress(100, 'Selesai!');
            setTimeout(() => {
                showSuccessState(data);
            }, 1000);
        } else {
            showErrorState(data.message || 'Terjadi kesalahan saat generate soal', data.error_type || 'unknown');
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        clearTimeout(generationTimeout);
        console.error('Error:', error);
        showErrorState('Terjadi kesalahan koneksi. Silakan periksa koneksi internet dan coba lagi.', 'network');
    });
}

function showErrorState(message, errorType = 'unknown') {
    document.getElementById('initial-state').classList.add('d-none');
    document.getElementById('loading-state').classList.add('d-none');
    document.getElementById('success-state').classList.add('d-none');
    document.getElementById('error-state').classList.remove('d-none');

    const errorMessageEl = document.getElementById('error-message');
    errorMessageEl.innerHTML = message;
}

function showSuccessState(data) {
    document.getElementById('initial-state').classList.add('d-none');
    document.getElementById('loading-state').classList.add('d-none');
    document.getElementById('error-state').classList.add('d-none');
    document.getElementById('success-state').classList.remove('d-none');

    document.getElementById('total-questions').textContent = data.total_questions;

    // Load questions
    loadQuestions(data.questions);
}

function simulateProgress() {
    let progress = 0;
    const messages = [
        'Memulai proses generate...',
        'Menghubungi AI Gemini...',
        'Menganalisis RPP...',
        'Membuat soal pilihan ganda...',
        'Membuat soal essay...',
        'Memvalidasi hasil...',
        'Menyelesaikan proses...'
    ];

    progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90; // Don't reach 100% until actually done

        const messageIndex = Math.floor((progress / 100) * messages.length);
        const message = messages[Math.min(messageIndex, messages.length - 1)];

        updateProgress(progress, message);
    }, 2000);
}

function updateProgress(percent, message) {
    document.getElementById('progress-bar').style.width = percent + '%';
    document.getElementById('progress-text').textContent = message;
}

function loadQuestions(questions) {
    if (!Array.isArray(questions)) {
        console.error('Questions data is not an array:', questions);
        showErrorState('Invalid questions data format');
        return;
    }

    const container = document.getElementById('questions-container');

    let html = `
        <!-- Hidden fields for exam data -->
        <?php foreach ($selected_rpp_ids as $rpp_id): ?>
            <input type="hidden" name="selected_rpps[]" value="<?= $rpp_id ?>">
        <?php endforeach; ?>

        <?php foreach ($exam_data as $key => $value): ?>
            <input type="hidden" name="exam_data[<?= $key ?>]" value="<?= htmlspecialchars($value) ?>">
        <?php endforeach; ?>

        <?php foreach ($config as $key => $value): ?>
            <input type="hidden" name="config[<?= $key ?>]" value="<?= htmlspecialchars($value) ?>">
        <?php endforeach; ?>

        <div class="row">
    `;

    questions.forEach((question, index) => {
        html += `
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <div class="form-check">
                            <input class="form-check-input question-checkbox" type="checkbox"
                                   name="selected_questions[]" value="${index}"
                                   id="question_${index}" checked>
                            <label class="form-check-label fw-bold" for="question_${index}">
                                Soal ${index + 1}
                                <span class="badge bg-${question.question_type === 'multiple_choice' ? 'primary' : 'success'} ms-2">
                                    ${question.question_type === 'multiple_choice' ? 'Pilihan Ganda' : 'Essay'}
                                </span>
                                <span class="badge bg-${getDifficultyColor(question.difficulty_level)} ms-1">
                                    ${getDifficultyLabel(question.difficulty_level)}
                                </span>
                                ${question.source_chapter ? `<span class="badge bg-info ms-1">Chapter ${question.source_chapter}</span>` : ''}
                            </label>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="question-text mb-3">
                            ${question.question_text.replace(/\n/g, '<br>')}
                        </div>`;

        if (question.question_type === 'multiple_choice' && question.options) {
            html += '<div class="options mb-3">';
            question.options.forEach(option => {
                const isCorrect = option.charAt(0) === question.correct_answer;
                html += `
                    <div class="option-item mb-1 ${isCorrect ? 'correct-answer' : ''}">
                        ${option}
                        ${isCorrect ? '<i class="fas fa-check-circle text-success ms-2"></i>' : ''}
                    </div>`;
            });
            html += '</div>';
            html += `<div class="answer-key">
                <small class="text-muted">
                    <strong>Kunci Jawaban:</strong> ${question.correct_answer}
                </small>
            </div>`;
        }

        if (question.explanation) {
            html += `<div class="mt-3">
                <small class="text-muted">
                    <strong>Penjelasan:</strong> ${question.explanation.replace(/\n/g, '<br>')}
                </small>
            </div>`;
        }

        if (question.source_rpp_id) {
            html += `<div class="mt-2">
                <small class="text-muted">
                    <strong>Source:</strong> Chapter ${question.source_chapter || 1}
                </small>
            </div>`;
        }

        html += `
                        <!-- Hidden inputs for question data -->
                        <input type="hidden" name="questions[${index}][source_rpp_id]" value="${question.source_rpp_id || ''}">
                        <input type="hidden" name="questions[${index}][source_chapter]" value="${question.source_chapter || ''}">
                        <input type="hidden" name="questions[${index}][question_text]" value="${escapeHtml(question.question_text)}">
                        <input type="hidden" name="questions[${index}][question_type]" value="${question.question_type}">
                        <input type="hidden" name="questions[${index}][options]" value="${escapeHtml(JSON.stringify(question.options || []))}">
                        <input type="hidden" name="questions[${index}][correct_answer]" value="${escapeHtml(question.correct_answer || '')}">
                        <input type="hidden" name="questions[${index}][difficulty_level]" value="${question.difficulty_level}">
                        <input type="hidden" name="questions[${index}][cognitive_level]" value="${question.cognitive_level || ''}">
                        <input type="hidden" name="questions[${index}][category]" value="${question.category || ''}">
                        <input type="hidden" name="questions[${index}][source_type]" value="${question.source_type || ''}">
                        <input type="hidden" name="questions[${index}][explanation]" value="${escapeHtml(question.explanation || '')}">
                    </div>
                </div>
            </div>`;
    });

    html += '</div>';
    container.innerHTML = html;
}

function selectAll() {
    document.querySelectorAll('.question-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function deselectAll() {
    document.querySelectorAll('.question-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
}

function getDifficultyColor(difficulty) {
    switch (difficulty) {
        case 'regular': return 'secondary';
        case 'hots_easy': return 'info';
        case 'hots_medium': return 'warning';
        case 'hots_hard': return 'danger';
        default: return 'secondary';
    }
}

function getDifficultyLabel(difficulty) {
    switch (difficulty) {
        case 'regular': return 'Regular';
        case 'hots_easy': return 'HOTS Mudah';
        case 'hots_medium': return 'HOTS Sedang';
        case 'hots_hard': return 'HOTS Tinggi';
        default: return 'Regular';
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>

<?php require_once '../template/footer.php'; ?>
