=== SAVE QUESTIONS DEBUG 2025-08-20 14:33:57 ===
POST keys: rpp_id, selected_questions, questions
Questions count: 10
Question 0 (MC):
  - Options raw: '["<PERSON><PERSON>","B. Volume","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>"]'
  - Options type: string
  - Options length: 59
  - Correct Answer: 'C'
Question 1 (MC):
  - Options raw: '["A. Menit","B. Jam","<PERSON>. <PERSON>ik","<PERSON><PERSON>","<PERSON><PERSON>"]'
  - Options type: string
  - Options length: 53
  - Correct Answer: 'C'
Question 2 (MC):
  - Options raw: '["<PERSON><PERSON>","<PERSON><PERSON> ve<PERSON>","<PERSON><PERSON>","<PERSON><PERSON> fi<PERSON>","<PERSON><PERSON> im<PERSON>"]'
  - Options type: string
  - Options length: 105
  - Correct Answer: 'C'
Question 3 (MC):
  - Options raw: '["A. 20 cm","B. 200 cm","C. 2000 cm","D. 0.2 cm","E. 0.02 cm"]'
  - Options type: string
  - Options length: 62
  - Correct Answer: 'B'
Question 4 (MC):
  - Options raw: '["A. 0.15 m","B. 1.5 m","C. 15 m","D. 1500 m","E. 15000 m"]'
  - Options type: string
  - Options length: 59
  - Correct Answer: 'B'
Question 5 (MC):
  - Options raw: '["A. 9 m²","B. 10 m²","C. 14 m²","D. 20 m²","E. 28 m²"]'
  - Options type: string
  - Options length: 60
  - Correct Answer: 'D'
Question 6 (MC):
  - Options raw: '["A. 10 m/s","B. 15 m/s","C. 20 m/s","D. 25 m/s","E. 30 m/s"]'
  - Options type: string
  - Options length: 61
  - Correct Answer: 'C'
Question 7 (MC):
  - Options raw: '["A. Menggunakan satu truk dengan kapasitas 2000 kg","B. Menggunakan dua truk dengan kapasitas masing-masing 1500 kg","C. Menggunakan tiga truk dengan kapasitas masing-masing 1500 kg","D. Menggunakan satu truk dengan kapasitas 5 ton","E. Menggunakan satu truk dengan kapasitas 2000 kg dan satu truk 1500 kg"]'
  - Options type: string
  - Options length: 308
  - Correct Answer: 'B'
Question 8 (MC):
  - Options raw: '["A. Mengambil nilai pengukuran pertama (20.1 cm)","B. Mengambil nilai pengukuran terakhir (20.0 cm)","C. Mengambil nilai pengukuran tengah (20.2 cm)","D. Menjumlahkan semua hasil pengukuran dan membaginya dengan 3","E. Mengambil nilai pengukuran yang paling sering muncul"]'
  - Options type: string
  - Options length: 274
  - Correct Answer: 'D'
Question 9 (MC):
  - Options raw: '["A. 248 K, Kelvin relevan karena menghindari nilai negatif","B. 273 K, Kelvin relevan karena menghindari nilai negatif","C. 298 K, Kelvin relevan karena menghindari nilai negatif","D. 25 K, Kelvin tidak relevan dalam perhitungan termodinamika","E. 0 K, Kelvin relevan karena menghindari nilai negatif"]'
  - Options type: string
  - Options length: 303
  - Correct Answer: 'C'
Processing MC question for index 0:
  - Initial options: '["A. Luas","B. Volume","C. Massa","D. Kecepatan","E. Gaya"]'
  - Initial options type: string
  - Final options for RppQuestion: array (
  0 => 'A. Luas',
  1 => 'B. Volume',
  2 => 'C. Massa',
  3 => 'D. Kecepatan',
  4 => 'E. Gaya',
)
  - Final correct_answer: 'C'
RppQuestion->create() processing:
  - Input options: array (
  0 => 'A. Luas',
  1 => 'B. Volume',
  2 => 'C. Massa',
  3 => 'D. Kecepatan',
  4 => 'E. Gaya',
)
  - Input options type: array
  - Array converted to JSON: ["A. Luas","B. Volume","C. Massa","D. Kecepatan","E. Gaya"]
  - Final options_json for DB: '["A. Luas","B. Volume","C. Massa","D. Kecepatan","E. Gaya"]'
Processing MC question for index 1:
  - Initial options: '["A. Menit","B. Jam","C. Detik","D. Hari","E. Tahun"]'
  - Initial options type: string
  - Final options for RppQuestion: array (
  0 => 'A. Menit',
  1 => 'B. Jam',
  2 => 'C. Detik',
  3 => 'D. Hari',
  4 => 'E. Tahun',
)
  - Final correct_answer: 'C'
RppQuestion->create() processing:
  - Input options: array (
  0 => 'A. Menit',
  1 => 'B. Jam',
  2 => 'C. Detik',
  3 => 'D. Hari',
  4 => 'E. Tahun',
)
  - Input options type: array
  - Array converted to JSON: ["A. Menit","B. Jam","C. Detik","D. Hari","E. Tahun"]
  - Final options_json for DB: '["A. Menit","B. Jam","C. Detik","D. Hari","E. Tahun"]'
Processing MC question for index 2:
  - Initial options: '["A. Besaran skalar","B. Besaran vektor","C. Besaran pokok","D. Konstanta fisika","E. Bilangan imajiner"]'
  - Initial options type: string
  - Final options for RppQuestion: array (
  0 => 'A. Besaran skalar',
  1 => 'B. Besaran vektor',
  2 => 'C. Besaran pokok',
  3 => 'D. Konstanta fisika',
  4 => 'E. Bilangan imajiner',
)
  - Final correct_answer: 'C'
RppQuestion->create() processing:
  - Input options: array (
  0 => 'A. Besaran skalar',
  1 => 'B. Besaran vektor',
  2 => 'C. Besaran pokok',
  3 => 'D. Konstanta fisika',
  4 => 'E. Bilangan imajiner',
)
  - Input options type: array
  - Array converted to JSON: ["A. Besaran skalar","B. Besaran vektor","C. Besaran pokok","D. Konstanta fisika","E. Bilangan imajiner"]
  - Final options_json for DB: '["A. Besaran skalar","B. Besaran vektor","C. Besaran pokok","D. Konstanta fisika","E. Bilangan imajiner"]'
Processing MC question for index 3:
  - Initial options: '["A. 20 cm","B. 200 cm","C. 2000 cm","D. 0.2 cm","E. 0.02 cm"]'
  - Initial options type: string
  - Final options for RppQuestion: array (
  0 => 'A. 20 cm',
  1 => 'B. 200 cm',
  2 => 'C. 2000 cm',
  3 => 'D. 0.2 cm',
  4 => 'E. 0.02 cm',
)
  - Final correct_answer: 'B'
RppQuestion->create() processing:
  - Input options: array (
  0 => 'A. 20 cm',
  1 => 'B. 200 cm',
  2 => 'C. 2000 cm',
  3 => 'D. 0.2 cm',
  4 => 'E. 0.02 cm',
)
  - Input options type: array
  - Array converted to JSON: ["A. 20 cm","B. 200 cm","C. 2000 cm","D. 0.2 cm","E. 0.02 cm"]
  - Final options_json for DB: '["A. 20 cm","B. 200 cm","C. 2000 cm","D. 0.2 cm","E. 0.02 cm"]'
Processing MC question for index 4:
  - Initial options: '["A. 0.15 m","B. 1.5 m","C. 15 m","D. 1500 m","E. 15000 m"]'
  - Initial options type: string
  - Final options for RppQuestion: array (
  0 => 'A. 0.15 m',
  1 => 'B. 1.5 m',
  2 => 'C. 15 m',
  3 => 'D. 1500 m',
  4 => 'E. 15000 m',
)
  - Final correct_answer: 'B'
RppQuestion->create() processing:
  - Input options: array (
  0 => 'A. 0.15 m',
  1 => 'B. 1.5 m',
  2 => 'C. 15 m',
  3 => 'D. 1500 m',
  4 => 'E. 15000 m',
)
  - Input options type: array
  - Array converted to JSON: ["A. 0.15 m","B. 1.5 m","C. 15 m","D. 1500 m","E. 15000 m"]
  - Final options_json for DB: '["A. 0.15 m","B. 1.5 m","C. 15 m","D. 1500 m","E. 15000 m"]'
Processing MC question for index 5:
  - Initial options: '["A. 9 m²","B. 10 m²","C. 14 m²","D. 20 m²","E. 28 m²"]'
  - Initial options type: string
  - Final options for RppQuestion: array (
  0 => 'A. 9 m²',
  1 => 'B. 10 m²',
  2 => 'C. 14 m²',
  3 => 'D. 20 m²',
  4 => 'E. 28 m²',
)
  - Final correct_answer: 'D'
RppQuestion->create() processing:
  - Input options: array (
  0 => 'A. 9 m²',
  1 => 'B. 10 m²',
  2 => 'C. 14 m²',
  3 => 'D. 20 m²',
  4 => 'E. 28 m²',
)
  - Input options type: array
  - Array converted to JSON: ["A. 9 m\u00b2","B. 10 m\u00b2","C. 14 m\u00b2","D. 20 m\u00b2","E. 28 m\u00b2"]
  - Final options_json for DB: '["A. 9 m\\u00b2","B. 10 m\\u00b2","C. 14 m\\u00b2","D. 20 m\\u00b2","E. 28 m\\u00b2"]'
Processing MC question for index 6:
  - Initial options: '["A. 10 m/s","B. 15 m/s","C. 20 m/s","D. 25 m/s","E. 30 m/s"]'
  - Initial options type: string
  - Final options for RppQuestion: array (
  0 => 'A. 10 m/s',
  1 => 'B. 15 m/s',
  2 => 'C. 20 m/s',
  3 => 'D. 25 m/s',
  4 => 'E. 30 m/s',
)
  - Final correct_answer: 'C'
RppQuestion->create() processing:
  - Input options: array (
  0 => 'A. 10 m/s',
  1 => 'B. 15 m/s',
  2 => 'C. 20 m/s',
  3 => 'D. 25 m/s',
  4 => 'E. 30 m/s',
)
  - Input options type: array
  - Array converted to JSON: ["A. 10 m\/s","B. 15 m\/s","C. 20 m\/s","D. 25 m\/s","E. 30 m\/s"]
  - Final options_json for DB: '["A. 10 m\\/s","B. 15 m\\/s","C. 20 m\\/s","D. 25 m\\/s","E. 30 m\\/s"]'
Processing MC question for index 7:
  - Initial options: '["A. Menggunakan satu truk dengan kapasitas 2000 kg","B. Menggunakan dua truk dengan kapasitas masing-masing 1500 kg","C. Menggunakan tiga truk dengan kapasitas masing-masing 1500 kg","D. Menggunakan satu truk dengan kapasitas 5 ton","E. Menggunakan satu truk dengan kapasitas 2000 kg dan satu truk 1500 kg"]'
  - Initial options type: string
  - Final options for RppQuestion: array (
  0 => 'A. Menggunakan satu truk dengan kapasitas 2000 kg',
  1 => 'B. Menggunakan dua truk dengan kapasitas masing-masing 1500 kg',
  2 => 'C. Menggunakan tiga truk dengan kapasitas masing-masing 1500 kg',
  3 => 'D. Menggunakan satu truk dengan kapasitas 5 ton',
  4 => 'E. Menggunakan satu truk dengan kapasitas 2000 kg dan satu truk 1500 kg',
)
  - Final correct_answer: 'B'
RppQuestion->create() processing:
  - Input options: array (
  0 => 'A. Menggunakan satu truk dengan kapasitas 2000 kg',
  1 => 'B. Menggunakan dua truk dengan kapasitas masing-masing 1500 kg',
  2 => 'C. Menggunakan tiga truk dengan kapasitas masing-masing 1500 kg',
  3 => 'D. Menggunakan satu truk dengan kapasitas 5 ton',
  4 => 'E. Menggunakan satu truk dengan kapasitas 2000 kg dan satu truk 1500 kg',
)
  - Input options type: array
  - Array converted to JSON: ["A. Menggunakan satu truk dengan kapasitas 2000 kg","B. Menggunakan dua truk dengan kapasitas masing-masing 1500 kg","C. Menggunakan tiga truk dengan kapasitas masing-masing 1500 kg","D. Menggunakan satu truk dengan kapasitas 5 ton","E. Menggunakan satu truk dengan kapasitas 2000 kg dan satu truk 1500 kg"]
  - Final options_json for DB: '["A. Menggunakan satu truk dengan kapasitas 2000 kg","B. Menggunakan dua truk dengan kapasitas masing-masing 1500 kg","C. Menggunakan tiga truk dengan kapasitas masing-masing 1500 kg","D. Menggunakan satu truk dengan kapasitas 5 ton","E. Menggunakan satu truk dengan kapasitas 2000 kg dan satu truk 1500 kg"]'
Processing MC question for index 8:
  - Initial options: '["A. Mengambil nilai pengukuran pertama (20.1 cm)","B. Mengambil nilai pengukuran terakhir (20.0 cm)","C. Mengambil nilai pengukuran tengah (20.2 cm)","D. Menjumlahkan semua hasil pengukuran dan membaginya dengan 3","E. Mengambil nilai pengukuran yang paling sering muncul"]'
  - Initial options type: string
  - Final options for RppQuestion: array (
  0 => 'A. Mengambil nilai pengukuran pertama (20.1 cm)',
  1 => 'B. Mengambil nilai pengukuran terakhir (20.0 cm)',
  2 => 'C. Mengambil nilai pengukuran tengah (20.2 cm)',
  3 => 'D. Menjumlahkan semua hasil pengukuran dan membaginya dengan 3',
  4 => 'E. Mengambil nilai pengukuran yang paling sering muncul',
)
  - Final correct_answer: 'D'
RppQuestion->create() processing:
  - Input options: array (
  0 => 'A. Mengambil nilai pengukuran pertama (20.1 cm)',
  1 => 'B. Mengambil nilai pengukuran terakhir (20.0 cm)',
  2 => 'C. Mengambil nilai pengukuran tengah (20.2 cm)',
  3 => 'D. Menjumlahkan semua hasil pengukuran dan membaginya dengan 3',
  4 => 'E. Mengambil nilai pengukuran yang paling sering muncul',
)
  - Input options type: array
  - Array converted to JSON: ["A. Mengambil nilai pengukuran pertama (20.1 cm)","B. Mengambil nilai pengukuran terakhir (20.0 cm)","C. Mengambil nilai pengukuran tengah (20.2 cm)","D. Menjumlahkan semua hasil pengukuran dan membaginya dengan 3","E. Mengambil nilai pengukuran yang paling sering muncul"]
  - Final options_json for DB: '["A. Mengambil nilai pengukuran pertama (20.1 cm)","B. Mengambil nilai pengukuran terakhir (20.0 cm)","C. Mengambil nilai pengukuran tengah (20.2 cm)","D. Menjumlahkan semua hasil pengukuran dan membaginya dengan 3","E. Mengambil nilai pengukuran yang paling sering muncul"]'
Processing MC question for index 9:
  - Initial options: '["A. 248 K, Kelvin relevan karena menghindari nilai negatif","B. 273 K, Kelvin relevan karena menghindari nilai negatif","C. 298 K, Kelvin relevan karena menghindari nilai negatif","D. 25 K, Kelvin tidak relevan dalam perhitungan termodinamika","E. 0 K, Kelvin relevan karena menghindari nilai negatif"]'
  - Initial options type: string
  - Final options for RppQuestion: array (
  0 => 'A. 248 K, Kelvin relevan karena menghindari nilai negatif',
  1 => 'B. 273 K, Kelvin relevan karena menghindari nilai negatif',
  2 => 'C. 298 K, Kelvin relevan karena menghindari nilai negatif',
  3 => 'D. 25 K, Kelvin tidak relevan dalam perhitungan termodinamika',
  4 => 'E. 0 K, Kelvin relevan karena menghindari nilai negatif',
)
  - Final correct_answer: 'C'
RppQuestion->create() processing:
  - Input options: array (
  0 => 'A. 248 K, Kelvin relevan karena menghindari nilai negatif',
  1 => 'B. 273 K, Kelvin relevan karena menghindari nilai negatif',
  2 => 'C. 298 K, Kelvin relevan karena menghindari nilai negatif',
  3 => 'D. 25 K, Kelvin tidak relevan dalam perhitungan termodinamika',
  4 => 'E. 0 K, Kelvin relevan karena menghindari nilai negatif',
)
  - Input options type: array
  - Array converted to JSON: ["A. 248 K, Kelvin relevan karena menghindari nilai negatif","B. 273 K, Kelvin relevan karena menghindari nilai negatif","C. 298 K, Kelvin relevan karena menghindari nilai negatif","D. 25 K, Kelvin tidak relevan dalam perhitungan termodinamika","E. 0 K, Kelvin relevan karena menghindari nilai negatif"]
  - Final options_json for DB: '["A. 248 K, Kelvin relevan karena menghindari nilai negatif","B. 273 K, Kelvin relevan karena menghindari nilai negatif","C. 298 K, Kelvin relevan karena menghindari nilai negatif","D. 25 K, Kelvin tidak relevan dalam perhitungan termodinamika","E. 0 K, Kelvin relevan karena menghindari nilai negatif"]'
