<?php
/**
 * AJAX endpoint for asynchronous multi-RPP question generation
 * This prevents browser timeouts and provides progress feedback
 */

// Set proper headers for AJAX response
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Increase PHP execution time and memory for this operation
set_time_limit(300); // 5 minutes
ini_set('memory_limit', '512M');

require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/Rpp.php';
require_once __DIR__ . '/../models/Guru.php';
require_once __DIR__ . '/../models/GeminiApi.php';

// Function to send JSON response
function sendResponse($success, $data = null, $message = '', $progress = 0, $error_type = 'unknown') {
    $response = [
        'success' => $success,
        'message' => $message,
        'progress' => $progress,
        'error_type' => $error_type,
        'timestamp' => date('Y-m-d H:i:s'),
        'server_info' => [
            'php_version' => PHP_VERSION,
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
            'execution_time' => round(microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'], 2) . 's'
        ]
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
        if (isset($data['questions'])) {
            $response['total_questions'] = count($data['questions']);
        }
    }
    
    echo json_encode($response);
    exit;
}

// Check authentication
try {
    checkGuruAccess();
} catch (Exception $e) {
    sendResponse(false, null, 'Authentication failed: ' . $e->getMessage(), 0, 'auth');
}

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, null, 'Invalid request method', 0, 'method');
}

// Start timing
$start_time = microtime(true);

try {
    // Validate required fields
    $required_fields = ['selected_rpps', 'exam_data', 'config'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field])) {
            sendResponse(false, null, "Missing required field: $field", 0, 'validation');
        }
    }

    // Extract data
    $selected_rpp_ids = $_POST['selected_rpps'];
    $exam_data = $_POST['exam_data'];
    $config = $_POST['config'];

    // Validate configuration
    $total_by_type = (int)$config['multiple_choice_count'] + (int)$config['essay_count'];
    $total_by_difficulty = (int)$config['regular_count'] + (int)$config['hots_easy_count'] + 
                          (int)$config['hots_medium_count'] + (int)$config['hots_hard_count'];
    $max_total = max($total_by_type, $total_by_difficulty);

    if ($max_total > 10 || $max_total === 0) {
        sendResponse(false, null, 'Total soal harus antara 1-10', 0, 'validation');
    }

    if ($total_by_type !== $total_by_difficulty) {
        sendResponse(false, null, 'Total jenis soal harus sama dengan total distribusi kesulitan', 0, 'validation');
    }

    // Get teacher data
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if (!($row = $stmt->fetch(PDO::FETCH_ASSOC))) {
        sendResponse(false, null, 'Data guru tidak ditemukan', 0, 'auth');
    }
    $guru_id = $row['id'];

    // Validate and get RPP data
    $rpp = new Rpp();
    $selected_rpps = [];
    foreach ($selected_rpp_ids as $rpp_id) {
        $stmt = $rpp->getOne($rpp_id);
        $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
            sendResponse(false, null, "RPP tidak ditemukan atau bukan milik Anda: $rpp_id", 0, 'auth');
        }
        
        $selected_rpps[] = $rpp_data;
    }

    if (count($selected_rpps) < 2) {
        sendResponse(false, null, 'Minimal 2 RPP diperlukan untuk multi-RPP exam', 0, 'validation');
    }

    // Send progress update
    sendResponse(true, null, 'Memulai proses generate...', 20);

    // Initialize Gemini API
    $geminiApi = new GeminiApi();
    
    // Test API connection first
    if (!$geminiApi->testConnection()) {
        sendResponse(false, null, 'Tidak dapat terhubung ke layanan AI. Silakan coba lagi nanti.', 0, 'api');
    }

    // Send progress update
    sendResponse(true, null, 'Terhubung ke AI, memulai generate...', 40);

    // Generate questions
    $generated_questions = $geminiApi->generateMultiRppQuestions($selected_rpps, $config);
    
    if (empty($generated_questions)) {
        sendResponse(false, null, 'Tidak ada soal yang berhasil dihasilkan. Silakan coba lagi dengan konfigurasi yang berbeda.', 0, 'generation');
    }

    // Calculate execution time
    $execution_time = round(microtime(true) - $start_time, 2);

    // Store questions in session for save page
    $_SESSION['generated_multi_rpp_questions'] = $generated_questions;
    $_SESSION['multi_rpp_question_config'] = $config;
    $_SESSION['multi_rpp_exam_data'] = $exam_data;
    $_SESSION['multi_rpp_selected_rpps'] = $selected_rpp_ids;
    $_SESSION['multi_rpp_generation_timestamp'] = time();

    // Send success response
    sendResponse(true, [
        'questions' => $generated_questions,
        'total_questions' => count($generated_questions),
        'execution_time' => $execution_time,
        'selected_rpps' => $selected_rpp_ids,
        'config' => $config,
        'exam_data' => $exam_data
    ], 'Soal berhasil dihasilkan!', 100);

} catch (Exception $e) {
    // Log the error for debugging
    error_log("Multi-RPP question generation error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    $error_message = $e->getMessage();
    $error_type = 'unknown';

    // Categorize error types for better user experience
    if (strpos($error_message, 'connection') !== false || strpos($error_message, 'timeout') !== false) {
        $error_type = 'connection';
        $error_message = 'Koneksi ke layanan AI terputus. Silakan periksa koneksi internet dan coba lagi.';
    } elseif (strpos($error_message, 'API') !== false || strpos($error_message, 'quota') !== false) {
        $error_type = 'api';
        $error_message = 'Layanan AI sedang sibuk atau kuota habis. Silakan coba lagi dalam beberapa menit.';
    } elseif (strpos($error_message, 'memory') !== false) {
        $error_type = 'memory';
        $error_message = 'Sistem kehabisan memori. Silakan kurangi jumlah soal atau coba lagi nanti.';
    } elseif (strpos($error_message, 'time') !== false) {
        $error_type = 'timeout';
        $error_message = 'Proses generate memakan waktu terlalu lama. Silakan coba lagi dengan jumlah soal yang lebih sedikit.';
    }

    sendResponse(false, null, $error_message, 0, $error_type);
}
?>
