<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/MultiRppExam.php';
require_once '../models/Guru.php';

// Cek jika bukan POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Akses tidak valid.";
    header("Location: multi_rpp_generate.php");
    exit();
}

// Validasi input
if (!isset($_POST['selected_rpps']) || !isset($_POST['exam_title']) || !isset($_POST['exam_type'])) {
    $_SESSION['error'] = "Data tidak lengkap.";
    header("Location: multi_rpp_generate.php");
    exit();
}

$selected_rpp_ids = $_POST['selected_rpps'];
$exam_title = $_POST['exam_title'];
$exam_type = $_POST['exam_type'];
$semester = $_POST['semester'];
$tahun_ajaran = $_POST['tahun_ajaran'];
$exam_duration = $_POST['exam_duration'];
$total_score = $_POST['total_score'];
$additional_notes = $_POST['additional_notes'] ?? '';

// Validasi minimal 2 RPP
if (count($selected_rpp_ids) < 2) {
    $_SESSION['error'] = "Silakan pilih minimal 2 RPP untuk ujian multi-chapter.";
    header("Location: multi_rpp_generate.php");
    exit();
}

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data RPP yang dipilih
$rpp = new Rpp();
$selected_rpps = [];
foreach ($selected_rpp_ids as $rpp_id) {
    $stmt = $rpp->getOne($rpp_id);
    $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
        $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
        header("Location: multi_rpp_generate.php");
        exit();
    }
    
    $selected_rpps[] = $rpp_data;
}

// Hitung total soal yang tersedia per RPP
$rppQuestion = new RppQuestion();
$total_available_questions = 0;
foreach ($selected_rpps as &$rpp_data) {
    $question_count = $rppQuestion->getCountByRppId($rpp_data['id']);
    $rpp_data['available_questions'] = $question_count;
    $total_available_questions += $question_count;
}

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-cog"></i> Konfigurasi Soal Multi-RPP
            </h5>
            <a href="multi_rpp_generate.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <!-- Exam Information Summary -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle"></i> Informasi Ujian</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td width="120"><strong>Judul Ujian:</strong></td>
                                            <td><?= htmlspecialchars($exam_title) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Jenis Ujian:</strong></td>
                                            <td><?= htmlspecialchars($exam_type) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Semester:</strong></td>
                                            <td><?= htmlspecialchars($semester) ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td width="120"><strong>Tahun Ajaran:</strong></td>
                                            <td><?= htmlspecialchars($tahun_ajaran) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Durasi:</strong></td>
                                            <td><?= htmlspecialchars($exam_duration) ?> menit</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Skor:</strong></td>
                                            <td><?= htmlspecialchars($total_score) ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Selected RPPs -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-layer-group"></i> RPP/Chapter yang Dipilih (<?= count($selected_rpps) ?> RPP)</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-sm">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>No</th>
                                            <th>Mata Pelajaran</th>
                                            <th>Tema/Chapter</th>
                                            <th>Materi Pokok</th>
                                            <th>Soal Tersedia</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($selected_rpps as $index => $rpp_data): ?>
                                            <tr>
                                                <td><?= $index + 1 ?></td>
                                                <td><?= htmlspecialchars($rpp_data['nama_mapel']) ?></td>
                                                <td><strong><?= htmlspecialchars($rpp_data['tema_subtema']) ?></strong></td>
                                                <td><?= htmlspecialchars($rpp_data['materi_pokok']) ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?= $rpp_data['available_questions'] ?> soal</span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-info-circle"></i>
                                <strong>Total soal tersedia:</strong> <?= $total_available_questions ?> soal dari <?= count($selected_rpps) ?> RPP
                            </div>
                        </div>
                    </div>

                    <!-- Question Configuration Form -->
                    <form id="configForm" action="multi_rpp_preview_async.php" method="POST">
                        <!-- Hidden fields for exam data -->
                        <?php foreach ($selected_rpp_ids as $rpp_id): ?>
                            <input type="hidden" name="selected_rpps[]" value="<?= $rpp_id ?>">
                        <?php endforeach; ?>
                        <input type="hidden" name="exam_title" value="<?= htmlspecialchars($exam_title) ?>">
                        <input type="hidden" name="exam_type" value="<?= htmlspecialchars($exam_type) ?>">
                        <input type="hidden" name="semester" value="<?= htmlspecialchars($semester) ?>">
                        <input type="hidden" name="tahun_ajaran" value="<?= htmlspecialchars($tahun_ajaran) ?>">
                        <input type="hidden" name="exam_duration" value="<?= htmlspecialchars($exam_duration) ?>">
                        <input type="hidden" name="total_score" value="<?= htmlspecialchars($total_score) ?>">
                        <input type="hidden" name="additional_notes" value="<?= htmlspecialchars($additional_notes) ?>">

                        <!-- Question Types -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-question-circle"></i> Jenis Soal</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="multiple_choice_count" class="form-label">Soal Pilihan Ganda</label>
                                            <input type="number" class="form-control" id="multiple_choice_count" name="multiple_choice_count"
                                                   min="0" max="10" value="5" onchange="updateTotal()">
                                        </div>

                                        <div class="mb-3" id="mc_options_group">
                                            <label for="multiple_choice_options" class="form-label">Jumlah Opsi Jawaban</label>
                                            <select class="form-select" id="multiple_choice_options" name="multiple_choice_options">
                                                <option value="2">A-B (2 opsi)</option>
                                                <option value="3">A-C (3 opsi)</option>
                                                <option value="4" selected>A-D (4 opsi)</option>
                                                <option value="5">A-E (5 opsi)</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="essay_count" class="form-label">Soal Essay</label>
                                            <input type="number" class="form-control" id="essay_count" name="essay_count"
                                                   min="0" max="10" value="2" onchange="updateTotal()">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Difficulty Levels -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-layer-group"></i> Tingkat Kesulitan</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="regular_count" class="form-label">Regular (C1-C3)</label>
                                            <input type="number" class="form-control" id="regular_count" name="regular_count"
                                                   min="0" max="10" value="4" onchange="updateTotal()">
                                            <small class="text-muted">Mengingat, Memahami, Menerapkan</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="hots_easy_count" class="form-label">HOTS Mudah (C4)</label>
                                            <input type="number" class="form-control" id="hots_easy_count" name="hots_easy_count"
                                                   min="0" max="10" value="2" onchange="updateTotal()">
                                            <small class="text-muted">Menganalisis - Level Dasar</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="hots_medium_count" class="form-label">HOTS Sedang (C4-C5)</label>
                                            <input type="number" class="form-control" id="hots_medium_count" name="hots_medium_count"
                                                   min="0" max="10" value="1" onchange="updateTotal()">
                                            <small class="text-muted">Menganalisis, Mengevaluasi</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="hots_hard_count" class="form-label">HOTS Tinggi (C5-C6)</label>
                                            <input type="number" class="form-control" id="hots_hard_count" name="hots_hard_count"
                                                   min="0" max="10" value="0" onchange="updateTotal()">
                                            <small class="text-muted">Mengevaluasi, Mencipta</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden field for distribution strategy -->
                        <input type="hidden" name="distribution_strategy" value="proportional">

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="generateBtn">
                                <i class="fas fa-magic"></i> Generate Soal
                            </button>
                        </div>
                    </form>
                </div>

                <div class="col-md-4">
                    <!-- Summary -->
                    <div class="card bg-light mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-calculator"></i> Ringkasan</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Pilihan Ganda:</span>
                                    <span id="mc_summary">5</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Essay:</span>
                                    <span id="essay_summary">2</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between fw-bold">
                                    <span>Total Soal:</span>
                                    <span id="total_questions">7</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <h6 class="small">Distribusi Kesulitan:</h6>
                                <div class="d-flex justify-content-between small">
                                    <span>Regular:</span>
                                    <span id="regular_summary">4</span>
                                </div>
                                <div class="d-flex justify-content-between small">
                                    <span>HOTS Mudah:</span>
                                    <span id="hots_easy_summary">2</span>
                                </div>
                                <div class="d-flex justify-content-between small">
                                    <span>HOTS Sedang:</span>
                                    <span id="hots_medium_summary">1</span>
                                </div>
                                <div class="d-flex justify-content-between small">
                                    <span>HOTS Tinggi:</span>
                                    <span id="hots_hard_summary">0</span>
                                </div>
                            </div>

                            <div class="alert alert-info small">
                                <i class="fas fa-info-circle"></i>
                                Soal akan didistribusikan dari <?= count($selected_rpps) ?> RPP yang dipilih
                            </div>
                        </div>
                    </div>
                    <div class="card bg-light">
                        <div class="card-header">
                            <h6 class="card-title mb-0"><i class="fas fa-info-circle"></i> Panduan Konfigurasi</h6>
                        </div>
                        <div class="card-body">
                            <h6>Tips Konfigurasi Soal:</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success"></i> Total soal maksimal 30 untuk performa optimal</li>
                                <li><i class="fas fa-check text-success"></i> Rasio PG:Essay yang disarankan 3:1 atau 4:1</li>
                                <li><i class="fas fa-check text-success"></i> Distribusi HOTS 30-40% dari total soal</li>
                                <li><i class="fas fa-check text-success"></i> Pastikan setiap RPP terwakili minimal 1 soal</li>
                            </ul>

                            <h6 class="mt-3">Distribusi Kesulitan Ideal:</h6>
                            <div class="small">
                                <div class="d-flex justify-content-between">
                                    <span>Regular (C1-C3):</span>
                                    <span>60-70%</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>HOTS Mudah (C4):</span>
                                    <span>15-20%</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>HOTS Sedang (C4-C5):</span>
                                    <span>10-15%</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>HOTS Tinggi (C5-C6):</span>
                                    <span>5-10%</span>
                                </div>
                            </div>

                            <hr>

                            <h6>Fitur AI Generation:</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-robot text-primary"></i> Integrasi antar chapter</li>
                                <li><i class="fas fa-robot text-primary"></i> Mapping Bloom's Taxonomy</li>
                                <li><i class="fas fa-robot text-primary"></i> Validasi kata kerja operasional</li>
                                <li><i class="fas fa-robot text-primary"></i> Distribusi proporsional otomatis</li>
                            </ul>

                            <div class="alert alert-warning small mt-3">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Catatan:</strong> Proses generate dapat memakan waktu 30-60 detik tergantung jumlah soal dan kompleksitas.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateTotal() {
    const mcCount = parseInt(document.getElementById('multiple_choice_count').value) || 0;
    const essayCount = parseInt(document.getElementById('essay_count').value) || 0;
    const regularCount = parseInt(document.getElementById('regular_count').value) || 0;
    const hotsEasyCount = parseInt(document.getElementById('hots_easy_count').value) || 0;
    const hotsMediumCount = parseInt(document.getElementById('hots_medium_count').value) || 0;
    const hotsHardCount = parseInt(document.getElementById('hots_hard_count').value) || 0;

    const totalByType = mcCount + essayCount;
    const totalByDifficulty = regularCount + hotsEasyCount + hotsMediumCount + hotsHardCount;
    const maxTotal = Math.max(totalByType, totalByDifficulty);

    // Update summary
    document.getElementById('mc_summary').textContent = mcCount;
    document.getElementById('essay_summary').textContent = essayCount;
    document.getElementById('total_questions').textContent = maxTotal;
    document.getElementById('regular_summary').textContent = regularCount;
    document.getElementById('hots_easy_summary').textContent = hotsEasyCount;
    document.getElementById('hots_medium_summary').textContent = hotsMediumCount;
    document.getElementById('hots_hard_summary').textContent = hotsHardCount;

    // Show/hide MC options
    const mcOptionsGroup = document.getElementById('mc_options_group');
    mcOptionsGroup.style.display = mcCount > 0 ? 'block' : 'none';

    // Validate totals match
    const generateBtn = document.getElementById('generateBtn');
    const isValid = totalByType === totalByDifficulty && maxTotal > 0 && maxTotal <= 10;
    generateBtn.disabled = !isValid;

    // Update button text based on validation
    if (totalByType !== totalByDifficulty) {
        generateBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Total Jenis ≠ Total Kesulitan';
        generateBtn.className = 'btn btn-warning btn-lg';
    } else if (maxTotal === 0) {
        generateBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Minimal 1 Soal';
        generateBtn.className = 'btn btn-warning btn-lg';
    } else if (maxTotal > 10) {
        generateBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Maksimal 10 Soal';
        generateBtn.className = 'btn btn-warning btn-lg';
    } else {
        generateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate Soal';
        generateBtn.className = 'btn btn-primary btn-lg';
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateTotal();

    // Add form validation
    const generateForm = document.getElementById('configForm');
    if (generateForm) {
        generateForm.addEventListener('submit', function(e) {
            const mcCount = parseInt(document.getElementById('multiple_choice_count').value) || 0;
            const essayCount = parseInt(document.getElementById('essay_count').value) || 0;
            const regularCount = parseInt(document.getElementById('regular_count').value) || 0;
            const hotsEasyCount = parseInt(document.getElementById('hots_easy_count').value) || 0;
            const hotsMediumCount = parseInt(document.getElementById('hots_medium_count').value) || 0;
            const hotsHardCount = parseInt(document.getElementById('hots_hard_count').value) || 0;

            const totalByType = mcCount + essayCount;
            const totalByDifficulty = regularCount + hotsEasyCount + hotsMediumCount + hotsHardCount;

            if (totalByType !== totalByDifficulty) {
                e.preventDefault();
                alert('Total jenis soal harus sama dengan total distribusi kesulitan');
                return false;
            }

            if (totalByType === 0) {
                e.preventDefault();
                alert('Total soal harus lebih dari 0');
                return false;
            }

            if (totalByType > 10) {
                e.preventDefault();
                alert('Total soal tidak boleh lebih dari 10');
                return false;
            }

            // Show loading
            const btn = document.getElementById('generateBtn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
            btn.disabled = true;
        });
    }
});
</script>

<style>
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.card-title {
    color: #495057;
}

.badge {
    font-size: 0.8rem;
}

#generateBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.alert {
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}
</style>

<?php require_once '../template/footer.php'; ?>
